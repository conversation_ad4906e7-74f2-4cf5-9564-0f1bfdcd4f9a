import 'dart:math';

import 'package:design_system/design_system.dart';

import 'package:auto_route/auto_route.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:juno_plus/pages/dashboard/therapy_analytics_chart_page.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:juno_plus/pages/dashboard/menstrual_cycle_dial.dart';
import 'package:juno_plus/pages/medications/medication_button.dart';
import 'package:juno_plus/routing/app_pages.gr.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:analytics/analytics.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../therapy/therapy_feedback_bottom_sheet.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;

import '../../custom_widgets/menstural_cycle_dial.dart';
import '../connectivity/connectivity_service.dart';

//global variable to be deleted
bool isShowcaseStarted = false;

@RoutePage()
class NewDashboardPage extends StatefulWidget {
  @override
  State<NewDashboardPage> createState() => _NewDashboardPageState();
}

class _NewDashboardPageState extends State<NewDashboardPage> {
  @override
  Widget build(BuildContext context) {
    return ShowCaseWidget(
        enableAutoScroll: true,
        enableShowcase: false,
        builder: (context) {
          return DashboardScaffold();
        });
  }
}

class DashboardScaffold extends StatefulWidget {
  const DashboardScaffold({super.key});

  @override
  State<DashboardScaffold> createState() => _DashboardScaffoldState();
}

class _DashboardScaffoldState extends State<DashboardScaffold> {
  final String formattedDate =
  DateFormat('EEEE, MMMM d\'\'th').format(DateTime.now());

  final GlobalKey _wholeCalendarKey = GlobalKey();

  final GlobalKey _currerntDayExplanationKey = GlobalKey();
  final GlobalKey _calendarButtonKey = GlobalKey();

  final GlobalKey _menstrualCycleDialKey = GlobalKey();

  final GlobalKey _painTrackingKey = GlobalKey();

  final GlobalKey _helpCenterKey = GlobalKey();

  final GlobalKey _lastUsedSettingKey = GlobalKey();

  final GlobalKey _medicationButtonKey = GlobalKey();

  final connectivityService = ConnectivityService();

  @override
  void initState() {
    super.initState();

    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   if (!isShowcaseStarted) {
    //     ShowCaseWidget.of(context)!.startShowCase([
    //       _wholeCalendarKey,
    //       // _currerntDayExplanationKey,
    //
    //       // _menstrualCycleDialKey,
    //        _painTrackingKey,
    //       _helpCenterKey,
    //       // _lastUsedSettingKey,
    //        _medicationButtonKey,
    //       _calendarButtonKey,
    //     ]);
    //     isShowcaseStarted = true;
    //   }
    // });
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
        stream: connectivityService.connectivityStream,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          return Scaffold(
            extendBodyBehindAppBar: true,
            appBar: CurvedAppBar(
              appBarColor: AppTheme.primaryColor,
              logoColor: Color(0xffFAF2DF),
              height: .35.sw,
              topRightIcon: GestureDetector(
                onTap: () {
                  context.router.push(NotificationsRoute());
                },
                child: Container(
                  height: 40,
                  width: 40,
                  decoration: BoxDecoration(
                    color: Color(0xffFAF2DF),
                    shape: BoxShape.circle,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(6.0),
                    child: Icon(
                      Icons.notifications_rounded,
                      color: Color(0xff30285D),
                    ),
                  ),
                ),
              ), // The height of your curved app bar
            ),
            body: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(22.0),
                child: Column(
                  children: [
                    SizedBox(
                      height: .35.sw,
                      width: 1.sw,
                    ),
                    Stack(
                      children: [
                        Container(
                          width: 1.sw,
                          height: 1.1.sw,
                        ),
                        GestureDetector(
                          key: Key('open_calendar_container'),
                          onTap: () {
                            context.router.push(PeriodTrackingCalendarRoute());
                          },
                          child: Container(
                            width: 1.sw,
                            height: 1.1.sw,
                            child: Padding(
                              padding: const EdgeInsets.all(20.0),
                              child: Column(
                                children: [
                                  Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                    CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        DateFormat('EEEE,')
                                            .format(DateTime.now()),
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyMedium!
                                            .copyWith(
                                          color: Color(0xff30285D),
                                          fontWeight: FontWeight.w400,
                                          fontSize: 24,
                                        ),
                                      ),
                                      Row(
                                        children: [
                                          Text(
                                            DateFormat('MMMM')
                                                .format(DateTime.now()),
                                            style: Theme.of(context)
                                                .textTheme
                                                .bodyMedium!
                                                .copyWith(
                                              color: Color(0xff30285D),
                                              fontWeight: FontWeight.w400,
                                              fontSize: 24,
                                            ),
                                          ),
                                          Text(
                                            ' ',
                                            style: Theme.of(context)
                                                .textTheme
                                                .bodyMedium!
                                                .copyWith(
                                              color: Color(0xff30285D),
                                              fontWeight: FontWeight.w400,
                                              fontSize: 24,
                                            ),
                                          ),
                                          Text(
                                            DateFormat('d')
                                                .format(DateTime.now()),
                                            style: Theme.of(context)
                                                .textTheme
                                                .bodyMedium!
                                                .copyWith(
                                              color: Color(0xff30285D),
                                              fontWeight: FontWeight.w400,
                                              fontSize: 24,
                                            ),
                                          ),
                                          Text(
                                            DateFormat.DAY == 1
                                                ? 'st'
                                                : DateFormat.DAY == 2
                                                ? 'nd'
                                                : DateFormat.DAY == 3
                                                ? 'rd'
                                                : 'th',
                                            style: Theme.of(context)
                                                .textTheme
                                                .bodyMedium!
                                                .copyWith(
                                              color: Color(0xff30285D),
                                              fontWeight: FontWeight.w400,
                                              fontSize: 24,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 20),
                                  Container(
                                      child: MenstrualCycleDial()),
                                  SizedBox(height: 20),
                                  // Commented out - main container is now clickable
                                  // Align(
                                  //   alignment: Alignment.centerLeft,
                                  //   child: Padding(
                                  //     padding: const EdgeInsets.symmetric(
                                  //         horizontal: 8.0),
                                  //     child: GestureDetector(
                                  //       key: Key('open_calendar_button'),
                                  //       onTap: () {
                                  //         context.router.push(
                                  //             PeriodTrackingCalendarRoute());
                                  //       },
                                  //       child: Showcase(
                                  //         key: _calendarButtonKey,
                                  //         title: 'Calendar',
                                  //         description:
                                  //             'Press the button to open the calendar.',
                                  //         descTextStyle: GoogleFonts.roboto(
                                  //           color: AppTheme.primaryColor,
                                  //           fontSize: 14,
                                  //           fontWeight: FontWeight.w400,
                                  //         ),
                                  //         tooltipBorderRadius:
                                  //             BorderRadius.circular(20),
                                  //         tooltipBackgroundColor:
                                  //             Color(0xffFAF2DF),
                                  //         tooltipPadding: EdgeInsets.all(20),
                                  //         titleTextStyle: GoogleFonts.roboto(
                                  //           color: AppTheme.primaryColor,
                                  //           fontSize: 20,
                                  //           fontWeight: FontWeight.w700,
                                  //         ),
                                  //         targetBorderRadius:
                                  //             BorderRadius.circular(40),
                                  //         child: Container(
                                  //           decoration: BoxDecoration(
                                  //             color: AppTheme.primaryColor,
                                  //             borderRadius: BorderRadius.all(
                                  //               Radius.circular(40),
                                  //             ),
                                  //           ),
                                  //           child: Padding(
                                  //             padding:
                                  //                 const EdgeInsets.symmetric(
                                  //                     horizontal: 15,
                                  //                     vertical: 8),
                                  //             child: Text(
                                  //               'Open Calendar',
                                  //               style: Theme.of(context)
                                  //                   .textTheme
                                  //                   .bodyMedium!
                                  //                   .copyWith(
                                  //                     color: Colors.white,
                                  //                   ),
                                  //             ),
                                  //           ),
                                  //         ),
                                  //       ),
                                  //     ),
                                  //   ),
                                  // ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        // Showcase container
                        Positioned(
                          top: 96,
                          left: 45,
                          child: Showcase(
                            key: _wholeCalendarKey,
                            title: 'Menstrual Cycle Dial',
                            description:
                            'This is the Menstrual Cycle Dial. It helps you track your menstrual cycle. Purple indicates the days you are on your period, and yellow arc indicates the days you are ovulating.',
                            descTextStyle: GoogleFonts.roboto(
                              color: AppTheme.primaryColor,
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                            tooltipBorderRadius: BorderRadius.circular(20),
                            tooltipBackgroundColor: Color(0xffFAF2DF),
                            tooltipPadding: EdgeInsets.all(20),
                            titleTextStyle: GoogleFonts.roboto(
                              color: AppTheme.primaryColor,
                              fontSize: 20,
                              fontWeight: FontWeight.w700,
                            ),
                            targetShapeBorder: CircleBorder(),
                            child: Container(
                              width: 261,
                              height: 261,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          width: .42.sw,
                          height: .95.sw,
                          child: Column(
                            children: [
                              GestureDetector(
                                onTap: () {
                                  context.router.push(HelpCenterHomeRoute());
                                },
                                child: Showcase(
                                  key: _helpCenterKey,
                                  title: 'Help Center',
                                  description:
                                  'Any questions? Click here to get help.',
                                  descTextStyle: GoogleFonts.roboto(
                                    color: AppTheme.primaryColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  tooltipBorderRadius:
                                  BorderRadius.circular(20),
                                  tooltipBackgroundColor: Color(0xffFAF2DF),
                                  tooltipPadding: EdgeInsets.all(20),
                                  tooltipPosition: TooltipPosition.top,
                                  titleTextStyle: GoogleFonts.roboto(
                                    color: AppTheme.primaryColor,
                                    fontSize: 20,
                                    fontWeight: FontWeight.w700,
                                  ),
                                  targetBorderRadius: BorderRadius.circular(32),
                                  child: Container(
                                      width: .42.sw,
                                      height: .13.sw,
                                      decoration: BoxDecoration(
                                          color: Color(0xffFAF2DF),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Color(0x40000000),
                                              // #00000040 in CSS corresponds to 0x40000000 in Flutter
                                              blurRadius: 4.0,
                                              // the blur radius
                                              offset: Offset(0,
                                                  1), // the x,y offset of the shadow
                                            ),
                                          ],
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(32))),
                                      child: Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: Row(
                                          children: [
                                            Container(
                                              height: 40,
                                              width: 40,
                                              decoration: BoxDecoration(
                                                color: Color(0xffE9DEFD),
                                                shape: BoxShape.circle,
                                              ),
                                              child: Padding(
                                                padding:
                                                const EdgeInsets.all(6.0),
                                                child: SvgPicture.asset(
                                                  'assets/home/<USER>',
                                                  height: 30,
                                                  width: 30,
                                                  colorFilter: ColorFilter.mode(
                                                    Color(0xff30285D),
                                                    BlendMode.srcIn,
                                                  ),
                                                ),
                                              ),
                                            ),
                                            SizedBox(
                                              width: 10,
                                            ),
                                            Text(
                                              'Help Center',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium!
                                                  .copyWith(
                                                color: Color(0xff30285D),
                                              ),
                                            ),
                                          ],
                                        ),
                                      )),
                                ),
                              ),
                              SizedBox(
                                height: 20,
                              ),
                              GestureDetector(
                                onTap: () {
                                  context.router
                                      .push(TherapyAnalyticsChartRoute());
                                },
                                child: Showcase(
                                  key: _painTrackingKey,
                                  title: 'Your Therapy',
                                  description:
                                  'This graph shows your device usage over the past week. Click to view more.',
                                  descTextStyle: GoogleFonts.roboto(
                                    color: AppTheme.primaryColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  tooltipBorderRadius:
                                  BorderRadius.circular(20),
                                  tooltipBackgroundColor: Color(0xffFAF2DF),
                                  tooltipPadding: EdgeInsets.all(20),
                                  tooltipPosition: TooltipPosition.top,
                                  titleTextStyle: GoogleFonts.roboto(
                                    color: AppTheme.primaryColor,
                                    fontSize: 20,
                                    fontWeight: FontWeight.w700,
                                  ),
                                  targetBorderRadius: BorderRadius.circular(32),
                                  child: Container(
                                    width: .42.sw,
                                    height: .65.sw,
                                    decoration: BoxDecoration(
                                        color: Color(0xffFAF2DF),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Color(0x40000000),
                                            // #00000040 in CSS corresponds to 0x40000000 in Flutter
                                            blurRadius: 4.0,
                                            // the blur radius
                                            offset: Offset(0,
                                                1), // the x,y offset of the shadow
                                          ),
                                        ],
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(32))),
                                    child: Padding(
                                      padding: const EdgeInsets.all(15.0),
                                      child: Column(
                                        mainAxisAlignment:
                                        MainAxisAlignment.start,
                                        crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                        children: [
                                          BlocBuilder<TherapyAnalyticsBloc,
                                              TherapyAnalyticsState>(
                                            builder: (context, state) {
                                              return state.when(
                                                initial: () => Container(),
                                                loading: () => Container(),
                                                loaded: (chartData, startDate,
                                                    endDate, viewType) {
                                                  // Check if there's any valid therapy data
                                                  if (chartData.isEmpty)
                                                    return Container();

                                                  final validSessions = chartData
                                                      .where((data) =>
                                                  data.tensLevel > 0 ||
                                                      data.heatLevel > 0 ||
                                                      (data.painLevelBefore !=
                                                          null &&
                                                          data.painLevelBefore! >
                                                              0) ||
                                                      (data.painLevelAfter !=
                                                          null &&
                                                          data.painLevelAfter! >
                                                              0))
                                                      .toList();

                                                  if (validSessions.isEmpty)
                                                    return Container();

                                                  return Text(
                                                    'Your Therapy',
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .bodyMedium!
                                                        .copyWith(
                                                      color:
                                                      Color(0xff30285D),
                                                    ),
                                                  );
                                                },
                                                error: (message) => Container(),
                                              );
                                            },
                                          ),
                                          SizedBox(
                                            height: 0,
                                          ),
                                          Container(
                                              height: .50.sw,
                                              child:
                                              TherapyAnalyticsChartMiniWidget()),
                                          // SizedBox(
                                          //   height: 15,
                                          // ),
                                          // Container(
                                          //   width: .32.sw,
                                          //   height: .09.sw,
                                          //   decoration: BoxDecoration(
                                          //     color: AppTheme.primaryColor,
                                          //     borderRadius: BorderRadius.all(
                                          //       Radius.circular(40),
                                          //     ),
                                          //   ),
                                          //   child: Center(
                                          //     child: Text('Add Data',style: Theme.of(context).textTheme.bodyMedium!.copyWith(color:Colors.white, ),),
                                          //   ),
                                          // ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          width: 15,
                        ),
                        Container(
                          width: .42.sw,
                          height: .95.sw,
                          child: Column(
                            children: [
                              // Container(
                              //   width: .38.sw,
                              //   height: .52.sw,
                              //   decoration: BoxDecoration(
                              //       color: Color(0xffFAF2DF),
                              //       boxShadow: [
                              //         BoxShadow(
                              //           color: Color(
                              //               0x40000000), // #00000040 in CSS corresponds to 0x40000000 in Flutter
                              //           blurRadius: 4.0, // the blur radius
                              //           offset: Offset(
                              //               0, 1), // the x,y offset of the shadow
                              //         ),
                              //       ],
                              //       borderRadius:
                              //       BorderRadius.all(Radius.circular(32))),
                              //   child: Column(
                              //     mainAxisAlignment: MainAxisAlignment.start,
                              //     children: [
                              //       SizedBox(
                              //         height: 10,
                              //       ),
                              //       Text(
                              //         'Last Used\nSetting',
                              //         style: Theme.of(context)
                              //             .textTheme
                              //             .bodyMedium!
                              //             .copyWith(
                              //           color: Color(0xff30285D),
                              //         ),
                              //         textAlign: TextAlign.center,
                              //       ),
                              //       SizedBox(
                              //         height: 5,
                              //       ),
                              //       Container(
                              //         padding: EdgeInsets.all(5),
                              //         decoration: BoxDecoration(
                              //             color: Colors.white,
                              //             borderRadius: BorderRadius.all(
                              //                 Radius.circular(10))),
                              //         height: .34.sw,
                              //         width: .28.sw,
                              //         child: Column(
                              //           mainAxisAlignment: MainAxisAlignment.center,
                              //           crossAxisAlignment:
                              //           CrossAxisAlignment.center,
                              //           children: [
                              //             Column(
                              //               children: [
                              //                 Row(
                              //                   mainAxisAlignment:
                              //                   MainAxisAlignment.center,
                              //                   children: [
                              //                     Text(
                              //                       '4',
                              //                       style: Theme.of(context)
                              //                           .textTheme
                              //                           .bodyMedium!
                              //                           .copyWith(
                              //                         color: Color(0xff30285D),
                              //                       ),
                              //                     ),
                              //                     SizedBox(
                              //                       width: 5,
                              //                     ),
                              //                     SvgPicture.asset(
                              //                       'assets/remote/remote_heat.svg',
                              //                       height: 30,
                              //                       width: 30,
                              //                     ),
                              //                   ],
                              //                 ),
                              //                 SizedBox(
                              //                   height: 6,
                              //                 ),
                              //                 Text(
                              //                   'Heat',
                              //                   style: Theme.of(context)
                              //                       .textTheme
                              //                       .bodyMedium!
                              //                       .copyWith(
                              //                     color: Color(0xff30285D),
                              //                   ),
                              //                 ),
                              //               ],
                              //             ),
                              //             SizedBox(
                              //               height: 10,
                              //             ),
                              //             Column(
                              //               children: [
                              //                 Row(
                              //                   mainAxisAlignment:
                              //                   MainAxisAlignment.center,
                              //                   children: [
                              //                     Text(
                              //                       '7',
                              //                       style: Theme.of(context)
                              //                           .textTheme
                              //                           .bodyMedium!
                              //                           .copyWith(
                              //                         color: Color(0xff30285D),
                              //                       ),
                              //                     ),
                              //                     SizedBox(
                              //                       width: 5,
                              //                     ),
                              //                     SvgPicture.asset(
                              //                       'assets/remote/remote_charge.svg',
                              //                       height: 25,
                              //                       width: 27,
                              //                     ),
                              //                   ],
                              //                 ),
                              //                 SizedBox(
                              //                   height: 6,
                              //                 ),
                              //                 Text(
                              //                   'Electric',
                              //                   style: Theme.of(context)
                              //                       .textTheme
                              //                       .bodyMedium!
                              //                       .copyWith(
                              //                     color: Color(0xff30285D),
                              //                   ),
                              //                 ),
                              //               ],
                              //             ),
                              //           ],
                              //         ),
                              //       )
                              //     ],
                              //   ),
                              // ),
                              // SizedBox(
                              //   height: 15,
                              // ),
                              Showcase(
                                  key: _medicationButtonKey,
                                  title: 'Medication',
                                  description:
                                  'This helps you track your medication, set reminders, and more. Click to view more.',
                                  descTextStyle: GoogleFonts.roboto(
                                    color: AppTheme.primaryColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  tooltipBorderRadius:
                                  BorderRadius.circular(20),
                                  tooltipBackgroundColor: Color(0xffFAF2DF),
                                  tooltipPadding: EdgeInsets.all(20),
                                  tooltipPosition: TooltipPosition.top,
                                  titleTextStyle: GoogleFonts.roboto(
                                    color: AppTheme.primaryColor,
                                    fontSize: 20,
                                    fontWeight: FontWeight.w700,
                                  ),
                                  targetBorderRadius: BorderRadius.circular(32),
                                  child: MedicationButton()),
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 40,
                    ),
                  ],
                ),
              ),
            ),
            // floatingActionButton: FloatingActionButton(
            //   onPressed: () => _showTherapyFeedbackTest(context),
            //   child: const Icon(Icons.feedback),
            //   tooltip: 'Test Therapy Feedback',
            // ),
          );
        });
  }

  void _showTherapyFeedbackTest(BuildContext context) {
    // Test the therapy feedback bottom sheet
    TherapyFeedbackBottomSheet.show(context, 'test_session_123');
  }
}
